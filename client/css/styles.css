body {
    background-image: url('../images/01.png');
    background-size: cover; /* Cover the entire viewport */
    background-position: center; /* Center the image */
    height: 100vh; /* Full height */
    margin: 0; /* Remove default margin */
    font-family: Arial, sans-serif; /* Set a default font */
}
.header-bar {
    background-color: #3f9ff8; /* Background color for the bar */
    color: white; /* Text color */
    padding: 20px; /* Padding around the text */
    text-align: center; /* Center the text */
}

h1 {
    margin: 0; /* Remove margin from h1 */
     text-align: center; 
}



#pc-container {
    
    display: flex; /* Use flexbox for layout */
    flex-wrap: wrap; /* Allow items to wrap to the next line */
    justify-content: center; /* Align items to the centre of the container */
    gap: 20px; /* Set a reasonable gap between PC elements */
    padding: 20px; /* Optional padding for the container */
    position: relative; /* Allows absolute positioning of children */
}

.pc {
    width: 350px;
    height: 320px;
    border: 2px solid #333;
    border-radius: 5px;
    background: #eee;
    position: relative; /* Allow lines to be drawn relative to PC */
    margin: 10px;
    transition: background 0.5s, transform 0.5s; /* Added transform transition for smoother effects */

    
}

.screen {
    width: 100%;
    height: 70%;
    background-color: #00ff00; /* Default green for active PCs */
    border-bottom: 2px solid #333;
    transition: background-color 0.5s ease; /* Smooth transition for background color */
}

.base {
    width: 100%;
    height: 30%;
    background-color: #333;
}

.idle .screen {
    background: #ffcc00; /* Color for idle state */
    box-shadow: 0 0 20px rgba(255, 204, 0, 0.8); /* Glowing effect */
    animation: none; /* No blinking effect for idle state */
}

.active .screen {
    animation: blink-green 1s infinite; /* Blinking effect */
}

@keyframes blink-green {
    0% { background: #00ff00; }
    50% { background: #00cc00; }
    100% { background: #00ff00; }
}

.off .screen {
    background-color: black; /* Set background to black when off */
    animation: none; /* No fading out animation */
}

.status {
    position: absolute;
    bottom: 25px; /* Position at the bottom of the PC */
    left: 50%;
    transform: translateX(-50%);
    font-weight: bold;
    color: #fff; /* White text color */
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7); /* Text shadow for better visibility */
}

.timer {
    font-size: 14px; /* Adjust the font size as needed */
    color: #333; /* Text color */
    position: absolute; /* Position it relative to the PC element */
    bottom: 5px; /* Adjust the distance from the bottom */
    left: 5px; /* Adjust the distance from the left */
    background-color: rgba(255, 255, 255, 0.7); /* Optional: add a background for better visibility */
    padding: 2px; /* Optional: add some padding */
    border-radius: 3px; /* Optional: round the corners */
}

.led {
    position: absolute;
    bottom: 10px; /* Positioning the LED at the base */
    right: 10px; /* Positioning the LED on the right */
    width: 10px; /* Width of the LED */
    height: 10px; /* Height of the LED */
    border-radius: 50%; /* Make it circular */
    background-color: #00ff00; /* Solid green when active */
    animation: flicker-led 0.1s infinite alternate; /* Rapid flickering effect */
}

.led.off {
    background-color: #0c010100; /* Grey when off */
    animation: none; /* Stop flickering when off */
}

/* Ensure the flicker effect is only for active LED */
.active .led {
    animation: flicker-led 0.1s infinite alternate; /* Rapid flickering effect */
}

@keyframes flicker-led {
    0% { opacity: 1; }
    100% { opacity: 0; } /* Flicker effect */
}

#reportIdleButton {
    display: block; /* Makes the button take the full width available */
    padding: 10px 20px; /* Add some padding */
    font-size: 18px; /* Increase font size */
    background: linear-gradient(145deg, #e6e6e6, #ffffff); /* Light gradient for glossy effect */
    border: 1px solid #bcbcbc; /* Light border */
    border-radius: 10px; /* Slightly rounded corners */
    box-shadow: 5px 5px 10px #d1d1d1, 
                -5px -5px 10px #ffffff; /* Embossed shadow effect */
    cursor: pointer; /* Change cursor to pointer */
    transition: all 0.3s ease; /* Smooth transition for hover effect */
    width: auto; /* Adjust the width to be auto */
}

#reportIdleButton:hover {
    background: linear-gradient(145deg, #ffffff, #e6e6e6); /* Invert gradient on hover */
    box-shadow: 2px 2px 5px #d1d1d1, 
                -2px -2px 5px #ffffff; /* Subtle hover shadow */
}
.connection-line {
    position: absolute;
    height: 2px; /* Thickness of the line */
    background-color: #000; /* Color of the line */
    z-index: -1; /* Ensure lines are behind the PCs */
}
#line-container {
    position: relative; /* Position for relative children */
    height: 500px; /* Adjust as necessary */
}

#report-window {
    margin-top: 20px; /* Space above the report window */
    padding: 10px;
    border: 1px solid #ccc;
    background-color: #f9f9f9;
    max-height: 200px; /* Limit the height */
    overflow-y: auto; /* Allow scrolling if necessary */
}

#log-messages {
    font-family: monospace; /* Use a monospace font for log messages */
    white-space: pre-wrap; /* Preserve spaces and line breaks */
}
