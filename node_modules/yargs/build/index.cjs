"use strict";var t=require("assert");class e extends Error{constructor(t){super(t||"yargs error"),this.name="YError",Error.captureStackTrace(this,e)}}let s,n=[];function i(t,o,a,h){s=h;let l={};if(Object.prototype.hasOwnProperty.call(t,"extends")){if("string"!=typeof t.extends)return l;const r=/\.json|\..*rc$/.test(t.extends);let h=null;if(r)h=function(t,e){return s.path.resolve(t,e)}(o,t.extends);else try{h=require.resolve(t.extends)}catch(e){return t}!function(t){if(n.indexOf(t)>-1)throw new e(`Circular extended configurations: '${t}'.`)}(h),n.push(h),l=r?JSON.parse(s.readFileSync(h,"utf8")):require(t.extends),delete t.extends,l=i(l,s.path.dirname(h),a,s)}return n=[],a?r(l,t):Object.assign({},l,t)}function r(t,e){const s={};function n(t){return t&&"object"==typeof t&&!Array.isArray(t)}Object.assign(s,t);for(const i of Object.keys(e))n(e[i])&&n(s[i])?s[i]=r(t[i],e[i]):s[i]=e[i];return s}function o(t){const e=t.replace(/\s{2,}/g," ").split(/\s+(?![^[]*]|[^<]*>)/),s=/\.*[\][<>]/g,n=e.shift();if(!n)throw new Error(`No command found in: ${t}`);const i={cmd:n.replace(s,""),demanded:[],optional:[]};return e.forEach(((t,n)=>{let r=!1;t=t.replace(/\s/g,""),/\.+[\]>]/.test(t)&&n===e.length-1&&(r=!0),/^\[/.test(t)?i.optional.push({cmd:t.replace(s,"").split("|"),variadic:r}):i.demanded.push({cmd:t.replace(s,"").split("|"),variadic:r})})),i}const a=["first","second","third","fourth","fifth","sixth"];function h(t,s,n){try{let i=0;const[r,a,h]="object"==typeof t?[{demanded:[],optional:[]},t,s]:[o(`cmd ${t}`),s,n],f=[].slice.call(a);for(;f.length&&void 0===f[f.length-1];)f.pop();const d=h||f.length;if(d<r.demanded.length)throw new e(`Not enough arguments provided. Expected ${r.demanded.length} but received ${f.length}.`);const u=r.demanded.length+r.optional.length;if(d>u)throw new e(`Too many arguments provided. Expected max ${u} but received ${d}.`);r.demanded.forEach((t=>{const e=l(f.shift());0===t.cmd.filter((t=>t===e||"*"===t)).length&&c(e,t.cmd,i),i+=1})),r.optional.forEach((t=>{if(0===f.length)return;const e=l(f.shift());0===t.cmd.filter((t=>t===e||"*"===t)).length&&c(e,t.cmd,i),i+=1}))}catch(t){console.warn(t.stack)}}function l(t){return Array.isArray(t)?"array":null===t?"null":typeof t}function c(t,s,n){throw new e(`Invalid ${a[n]||"manyith"} argument. Expected ${s.join(" or ")} but received ${t}.`)}function f(t){return!!t&&!!t.then&&"function"==typeof t.then}function d(t,e,s,n){s.assert.notStrictEqual(t,e,n)}function u(t,e){e.assert.strictEqual(typeof t,"string")}function p(t){return Object.keys(t)}function g(t={},e=(()=>!0)){const s={};return p(t).forEach((n=>{e(n,t[n])&&(s[n]=t[n])})),s}function m(){return process.versions.electron&&!process.defaultApp?0:1}function y(){return process.argv[m()]}var b=Object.freeze({__proto__:null,hideBin:function(t){return t.slice(m()+1)},getProcessArgvBin:y});function v(t,e,s,n){if("a"===s&&!n)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!n:!e.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===s?n:"a"===s?n.call(t):n?n.value:e.get(t)}function O(t,e,s,n,i){if("m"===n)throw new TypeError("Private method is not writable");if("a"===n&&!i)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!i:!e.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?i.call(t,s):i?i.value=s:e.set(t,s),s}class w{constructor(t){this.globalMiddleware=[],this.frozens=[],this.yargs=t}addMiddleware(t,e,s=!0,n=!1){if(h("<array|function> [boolean] [boolean] [boolean]",[t,e,s],arguments.length),Array.isArray(t)){for(let n=0;n<t.length;n++){if("function"!=typeof t[n])throw Error("middleware must be a function");const i=t[n];i.applyBeforeValidation=e,i.global=s}Array.prototype.push.apply(this.globalMiddleware,t)}else if("function"==typeof t){const i=t;i.applyBeforeValidation=e,i.global=s,i.mutates=n,this.globalMiddleware.push(t)}return this.yargs}addCoerceMiddleware(t,e){const s=this.yargs.getAliases();return this.globalMiddleware=this.globalMiddleware.filter((t=>{const n=[...s[e]||[],e];return!t.option||!n.includes(t.option)})),t.option=e,this.addMiddleware(t,!0,!0,!0)}getMiddleware(){return this.globalMiddleware}freeze(){this.frozens.push([...this.globalMiddleware])}unfreeze(){const t=this.frozens.pop();void 0!==t&&(this.globalMiddleware=t)}reset(){this.globalMiddleware=this.globalMiddleware.filter((t=>t.global))}}function C(t,e,s,n){return s.reduce(((t,s)=>{if(s.applyBeforeValidation!==n)return t;if(s.mutates){if(s.applied)return t;s.applied=!0}if(f(t))return t.then((t=>Promise.all([t,s(t,e)]))).then((([t,e])=>Object.assign(t,e)));{const n=s(t,e);return f(n)?n.then((e=>Object.assign(t,e))):Object.assign(t,n)}}),t)}function j(t,e,s=(t=>{throw t})){try{const s="function"==typeof t?t():t;return f(s)?s.then((t=>e(t))):e(s)}catch(t){return s(t)}}const _=/(^\*)|(^\$0)/;class M{constructor(t,e,s,n){this.requireCache=new Set,this.handlers={},this.aliasMap={},this.frozens=[],this.shim=n,this.usage=t,this.globalMiddleware=s,this.validation=e}addDirectory(t,e,s,n){"boolean"!=typeof(n=n||{}).recurse&&(n.recurse=!1),Array.isArray(n.extensions)||(n.extensions=["js"]);const i="function"==typeof n.visit?n.visit:t=>t;n.visit=(t,e,s)=>{const n=i(t,e,s);if(n){if(this.requireCache.has(e))return n;this.requireCache.add(e),this.addHandler(n)}return n},this.shim.requireDirectory({require:e,filename:s},t,n)}addHandler(t,e,s,n,i,r){let a=[];const h=function(t){return t?t.map((t=>(t.applyBeforeValidation=!1,t))):[]}(i);if(n=n||(()=>{}),Array.isArray(t))if(function(t){return t.every((t=>"string"==typeof t))}(t))[t,...a]=t;else for(const e of t)this.addHandler(e);else{if(function(t){return"object"==typeof t&&!Array.isArray(t)}(t)){let e=Array.isArray(t.command)||"string"==typeof t.command?t.command:this.moduleName(t);return t.aliases&&(e=[].concat(e).concat(t.aliases)),void this.addHandler(e,this.extractDesc(t),t.builder,t.handler,t.middlewares,t.deprecated)}if(k(s))return void this.addHandler([t].concat(a),e,s.builder,s.handler,s.middlewares,s.deprecated)}if("string"==typeof t){const i=o(t);a=a.map((t=>o(t).cmd));let l=!1;const c=[i.cmd].concat(a).filter((t=>!_.test(t)||(l=!0,!1)));0===c.length&&l&&c.push("$0"),l&&(i.cmd=c[0],a=c.slice(1),t=t.replace(_,i.cmd)),a.forEach((t=>{this.aliasMap[t]=i.cmd})),!1!==e&&this.usage.command(t,e,l,a,r),this.handlers[i.cmd]={original:t,description:e,handler:n,builder:s||{},middlewares:h,deprecated:r,demanded:i.demanded,optional:i.optional},l&&(this.defaultCommand=this.handlers[i.cmd])}}getCommandHandlers(){return this.handlers}getCommands(){return Object.keys(this.handlers).concat(Object.keys(this.aliasMap))}hasDefaultCommand(){return!!this.defaultCommand}runCommand(t,e,s,n,i,r){const o=this.handlers[t]||this.handlers[this.aliasMap[t]]||this.defaultCommand,a=e.getInternalMethods().getContext(),h=a.commands.slice(),l=!t;t&&(a.commands.push(t),a.fullCommands.push(o.original));const c=this.applyBuilderUpdateUsageAndParse(l,o,e,s.aliases,h,n,i,r);return f(c)?c.then((t=>this.applyMiddlewareAndGetResult(l,o,t.innerArgv,a,i,t.aliases,e))):this.applyMiddlewareAndGetResult(l,o,c.innerArgv,a,i,c.aliases,e)}applyBuilderUpdateUsageAndParse(t,e,s,n,i,r,o,a){const h=e.builder;let l=s;if(E(h)){const c=h(s.getInternalMethods().reset(n),a);if(f(c))return c.then((n=>{var a;return l=(a=n)&&"function"==typeof a.getInternalMethods?n:s,this.parseAndUpdateUsage(t,e,l,i,r,o)}))}else(function(t){return"object"==typeof t})(h)&&(l=s.getInternalMethods().reset(n),Object.keys(e.builder).forEach((t=>{l.option(t,h[t])})));return this.parseAndUpdateUsage(t,e,l,i,r,o)}parseAndUpdateUsage(t,e,s,n,i,r){t&&s.getInternalMethods().getUsageInstance().unfreeze(),this.shouldUpdateUsage(s)&&s.getInternalMethods().getUsageInstance().usage(this.usageFromParentCommandsCommandHandler(n,e),e.description);const o=s.getInternalMethods().runYargsParserAndExecuteCommands(null,void 0,!0,i,r);return f(o)?o.then((t=>({aliases:s.parsed.aliases,innerArgv:t}))):{aliases:s.parsed.aliases,innerArgv:o}}shouldUpdateUsage(t){return!t.getInternalMethods().getUsageInstance().getUsageDisabled()&&0===t.getInternalMethods().getUsageInstance().getUsage().length}usageFromParentCommandsCommandHandler(t,e){const s=_.test(e.original)?e.original.replace(_,"").trim():e.original,n=t.filter((t=>!_.test(t)));return n.push(s),`$0 ${n.join(" ")}`}applyMiddlewareAndGetResult(t,e,s,n,i,r,o){let a={};if(i)return s;o.getInternalMethods().getHasOutput()||(a=this.populatePositionals(e,s,n,o));const h=this.globalMiddleware.getMiddleware().slice(0).concat(e.middlewares);if(s=C(s,o,h,!0),!o.getInternalMethods().getHasOutput()){const e=o.getInternalMethods().runValidation(r,a,o.parsed.error,t);s=j(s,(t=>(e(t),t)))}if(e.handler&&!o.getInternalMethods().getHasOutput()){o.getInternalMethods().setHasOutput();const n=!!o.getOptions().configuration["populate--"];o.getInternalMethods().postProcess(s,n,!1,!1),s=j(s=C(s,o,h,!1),(t=>{const s=e.handler(t);return f(s)?s.then((()=>t)):t})),t||o.getInternalMethods().getUsageInstance().cacheHelpMessage(),f(s)&&!o.getInternalMethods().hasParseCallback()&&s.catch((t=>{try{o.getInternalMethods().getUsageInstance().fail(null,t)}catch(t){}}))}return t||(n.commands.pop(),n.fullCommands.pop()),s}populatePositionals(t,e,s,n){e._=e._.slice(s.commands.length);const i=t.demanded.slice(0),r=t.optional.slice(0),o={};for(this.validation.positionalCount(i.length,e._.length);i.length;){const t=i.shift();this.populatePositional(t,e,o)}for(;r.length;){const t=r.shift();this.populatePositional(t,e,o)}return e._=s.commands.concat(e._.map((t=>""+t))),this.postProcessPositionals(e,o,this.cmdToParseOptions(t.original),n),o}populatePositional(t,e,s){const n=t.cmd[0];t.variadic?s[n]=e._.splice(0).map(String):e._.length&&(s[n]=[String(e._.shift())])}cmdToParseOptions(t){const e={array:[],default:{},alias:{},demand:{}},s=o(t);return s.demanded.forEach((t=>{const[s,...n]=t.cmd;t.variadic&&(e.array.push(s),e.default[s]=[]),e.alias[s]=n,e.demand[s]=!0})),s.optional.forEach((t=>{const[s,...n]=t.cmd;t.variadic&&(e.array.push(s),e.default[s]=[]),e.alias[s]=n})),e}postProcessPositionals(t,e,s,n){const i=Object.assign({},n.getOptions());i.default=Object.assign(s.default,i.default);for(const t of Object.keys(s.alias))i.alias[t]=(i.alias[t]||[]).concat(s.alias[t]);i.array=i.array.concat(s.array),i.config={};const r=[];if(Object.keys(e).forEach((t=>{e[t].map((e=>{i.configuration["unknown-options-as-args"]&&(i.key[t]=!0),r.push(`--${t}`),r.push(e)}))})),!r.length)return;const o=Object.assign({},i.configuration,{"populate--":!1}),a=this.shim.Parser.detailed(r,Object.assign({},i,{configuration:o}));if(a.error)n.getInternalMethods().getUsageInstance().fail(a.error.message,a.error);else{const s=Object.keys(e);Object.keys(e).forEach((t=>{s.push(...a.aliases[t])}));const i=n.getOptions().default;Object.keys(a.argv).forEach((n=>{s.includes(n)&&(e[n]||(e[n]=a.argv[n]),!Object.prototype.hasOwnProperty.call(i,n)&&Object.prototype.hasOwnProperty.call(t,n)&&Object.prototype.hasOwnProperty.call(a.argv,n)&&(Array.isArray(t[n])||Array.isArray(a.argv[n]))?t[n]=[].concat(t[n],a.argv[n]):t[n]=a.argv[n])}))}}runDefaultBuilderOn(t){if(!this.defaultCommand)return;if(this.shouldUpdateUsage(t)){const e=_.test(this.defaultCommand.original)?this.defaultCommand.original:this.defaultCommand.original.replace(/^[^[\]<>]*/,"$0 ");t.getInternalMethods().getUsageInstance().usage(e,this.defaultCommand.description)}const e=this.defaultCommand.builder;if(E(e))return e(t,!0);k(e)||Object.keys(e).forEach((s=>{t.option(s,e[s])}))}moduleName(t){const e=function(t){if("undefined"==typeof require)return null;for(let e,s=0,n=Object.keys(require.cache);s<n.length;s++)if(e=require.cache[n[s]],e.exports===t)return e;return null}(t);if(!e)throw new Error(`No command name given for module: ${this.shim.inspect(t)}`);return this.commandFromFilename(e.filename)}commandFromFilename(t){return this.shim.path.basename(t,this.shim.path.extname(t))}extractDesc({describe:t,description:e,desc:s}){for(const n of[t,e,s]){if("string"==typeof n||!1===n)return n;d(n,!0,this.shim)}return!1}freeze(){this.frozens.push({handlers:this.handlers,aliasMap:this.aliasMap,defaultCommand:this.defaultCommand})}unfreeze(){const t=this.frozens.pop();d(t,void 0,this.shim),({handlers:this.handlers,aliasMap:this.aliasMap,defaultCommand:this.defaultCommand}=t)}reset(){return this.handlers={},this.aliasMap={},this.defaultCommand=void 0,this.requireCache=new Set,this}}function k(t){return"object"==typeof t&&!!t.builder&&"function"==typeof t.handler}function E(t){return"function"==typeof t}function x(t){"undefined"!=typeof process&&[process.stdout,process.stderr].forEach((e=>{const s=e;s._handle&&s.isTTY&&"function"==typeof s._handle.setBlocking&&s._handle.setBlocking(t)}))}function S(t){return"boolean"==typeof t}function $(t,s){const n=s.y18n.__,i={},r=[];i.failFn=function(t){r.push(t)};let o=null,a=!0;i.showHelpOnFail=function(t=!0,e){const[s,n]="string"==typeof t?[!0,t]:[t,e];return o=n,a=s,i};let h=!1;i.fail=function(s,n){const l=t.getInternalMethods().getLoggerInstance();if(!r.length){if(t.getExitProcess()&&x(!0),h||(h=!0,a&&(t.showHelp("error"),l.error()),(s||n)&&l.error(s||n),o&&((s||n)&&l.error(""),l.error(o))),n=n||new e(s),t.getExitProcess())return t.exit(1);if(t.getInternalMethods().hasParseCallback())return t.exit(1,n);throw n}for(let t=r.length-1;t>=0;--t){const e=r[t];if(S(e)){if(n)throw n;if(s)throw Error(s)}else e(s,n,i)}};let l=[],c=!1;i.usage=(t,e)=>null===t?(c=!0,l=[],i):(c=!1,l.push([t,e||""]),i),i.getUsage=()=>l,i.getUsageDisabled=()=>c,i.getPositionalGroupName=()=>n("Positionals:");let f=[];i.example=(t,e)=>{f.push([t,e||""])};let d=[];i.command=function(t,e,s,n,i=!1){s&&(d=d.map((t=>(t[2]=!1,t)))),d.push([t,e||"",s,n,i])},i.getCommands=()=>d;let u={};i.describe=function(t,e){Array.isArray(t)?t.forEach((t=>{i.describe(t,e)})):"object"==typeof t?Object.keys(t).forEach((e=>{i.describe(e,t[e])})):u[t]=e},i.getDescriptions=()=>u;let p=[];i.epilog=t=>{p.push(t)};let m,y=!1;function b(){return y||(m=function(){const t=80;return s.process.stdColumns?Math.min(t,s.process.stdColumns):t}(),y=!0),m}i.wrap=t=>{y=!0,m=t};const v="__yargsString__:";function O(t,e,n){let i=0;return Array.isArray(t)||(t=Object.values(t).map((t=>[t]))),t.forEach((t=>{i=Math.max(s.stringWidth(n?`${n} ${I(t[0])}`:I(t[0]))+P(t[0]),i)})),e&&(i=Math.min(i,parseInt((.5*e).toString(),10))),i}let w;function C(e){return t.getOptions().hiddenOptions.indexOf(e)<0||t.parsed.argv[t.getOptions().showHiddenOpt]}function j(t,e){let s=`[${n("default:")} `;if(void 0===t&&!e)return null;if(e)s+=e;else switch(typeof t){case"string":s+=`"${t}"`;break;case"object":s+=JSON.stringify(t);break;default:s+=t}return`${s}]`}i.deferY18nLookup=t=>v+t,i.help=function(){if(w)return w;!function(){const e=t.getDemandedOptions(),s=t.getOptions();(Object.keys(s.alias)||[]).forEach((n=>{s.alias[n].forEach((r=>{u[r]&&i.describe(n,u[r]),r in e&&t.demandOption(n,e[r]),s.boolean.includes(r)&&t.boolean(n),s.count.includes(r)&&t.count(n),s.string.includes(r)&&t.string(n),s.normalize.includes(r)&&t.normalize(n),s.array.includes(r)&&t.array(n),s.number.includes(r)&&t.number(n)}))}))}();const e=t.customScriptName?t.$0:s.path.basename(t.$0),r=t.getDemandedOptions(),o=t.getDemandedCommands(),a=t.getDeprecatedOptions(),h=t.getGroups(),g=t.getOptions();let m=[];m=m.concat(Object.keys(u)),m=m.concat(Object.keys(r)),m=m.concat(Object.keys(o)),m=m.concat(Object.keys(g.default)),m=m.filter(C),m=Object.keys(m.reduce(((t,e)=>("_"!==e&&(t[e]=!0),t)),{}));const y=b(),_=s.cliui({width:y,wrap:!!y});if(!c)if(l.length)l.forEach((t=>{_.div({text:`${t[0].replace(/\$0/g,e)}`}),t[1]&&_.div({text:`${t[1]}`,padding:[1,0,0,0]})})),_.div();else if(d.length){let t=null;t=o._?`${e} <${n("command")}>\n`:`${e} [${n("command")}]\n`,_.div(`${t}`)}if(d.length>1||1===d.length&&!d[0][2]){_.div(n("Commands:"));const s=t.getInternalMethods().getContext(),i=s.commands.length?`${s.commands.join(" ")} `:"";!0===t.getInternalMethods().getParserConfiguration()["sort-commands"]&&(d=d.sort(((t,e)=>t[0].localeCompare(e[0])))),d.forEach((t=>{const s=`${e} ${i}${t[0].replace(/^\$0 ?/,"")}`;_.span({text:s,padding:[0,2,0,2],width:O(d,y,`${e}${i}`)+4},{text:t[1]});const r=[];t[2]&&r.push(`[${n("default")}]`),t[3]&&t[3].length&&r.push(`[${n("aliases:")} ${t[3].join(", ")}]`),t[4]&&("string"==typeof t[4]?r.push(`[${n("deprecated: %s",t[4])}]`):r.push(`[${n("deprecated")}]`)),r.length?_.div({text:r.join(" "),padding:[0,0,0,2],align:"right"}):_.div()})),_.div()}const M=(Object.keys(g.alias)||[]).concat(Object.keys(t.parsed.newAliases)||[]);m=m.filter((e=>!t.parsed.newAliases[e]&&M.every((t=>-1===(g.alias[t]||[]).indexOf(e)))));const k=n("Options:");h[k]||(h[k]=[]),function(t,e,s,n){let i=[],r=null;Object.keys(s).forEach((t=>{i=i.concat(s[t])})),t.forEach((t=>{r=[t].concat(e[t]),r.some((t=>-1!==i.indexOf(t)))||s[n].push(t)}))}(m,g.alias,h,k);const E=t=>/^--/.test(I(t)),x=Object.keys(h).filter((t=>h[t].length>0)).map((t=>({groupName:t,normalizedKeys:h[t].filter(C).map((t=>{if(M.includes(t))return t;for(let e,s=0;void 0!==(e=M[s]);s++)if((g.alias[e]||[]).includes(t))return e;return t}))}))).filter((({normalizedKeys:t})=>t.length>0)).map((({groupName:t,normalizedKeys:e})=>{const s=e.reduce(((e,s)=>(e[s]=[s].concat(g.alias[s]||[]).map((e=>t===i.getPositionalGroupName()?e:(/^[0-9]$/.test(e)?g.boolean.includes(s)?"-":"--":e.length>1?"--":"-")+e)).sort(((t,e)=>E(t)===E(e)?0:E(t)?1:-1)).join(", "),e)),{});return{groupName:t,normalizedKeys:e,switches:s}}));if(x.filter((({groupName:t})=>t!==i.getPositionalGroupName())).some((({normalizedKeys:t,switches:e})=>!t.every((t=>E(e[t])))))&&x.filter((({groupName:t})=>t!==i.getPositionalGroupName())).forEach((({normalizedKeys:t,switches:e})=>{t.forEach((t=>{var s,n;E(e[t])&&(e[t]=(s=e[t],n="-x, ".length,A(s)?{text:s.text,indentation:s.indentation+n}:{text:s,indentation:n}))}))})),x.forEach((({groupName:t,normalizedKeys:e,switches:s})=>{_.div(t),e.forEach((t=>{const e=s[t];let o=u[t]||"",h=null;o.includes(v)&&(o=n(o.substring(v.length))),g.boolean.includes(t)&&(h=`[${n("boolean")}]`),g.count.includes(t)&&(h=`[${n("count")}]`),g.string.includes(t)&&(h=`[${n("string")}]`),g.normalize.includes(t)&&(h=`[${n("string")}]`),g.array.includes(t)&&(h=`[${n("array")}]`),g.number.includes(t)&&(h=`[${n("number")}]`);const l=[t in a?(c=a[t],"string"==typeof c?`[${n("deprecated: %s",c)}]`:`[${n("deprecated")}]`):null,h,t in r?`[${n("required")}]`:null,g.choices&&g.choices[t]?`[${n("choices:")} ${i.stringifiedValues(g.choices[t])}]`:null,j(g.default[t],g.defaultDescription[t])].filter(Boolean).join(" ");var c;_.span({text:I(e),padding:[0,2,0,2+P(e)],width:O(s,y)+4},o),l?_.div({text:l,padding:[0,0,0,2],align:"right"}):_.div()})),_.div()})),f.length&&(_.div(n("Examples:")),f.forEach((t=>{t[0]=t[0].replace(/\$0/g,e)})),f.forEach((t=>{""===t[1]?_.div({text:t[0],padding:[0,2,0,2]}):_.div({text:t[0],padding:[0,2,0,2],width:O(f,y)+4},{text:t[1]})})),_.div()),p.length>0){const t=p.map((t=>t.replace(/\$0/g,e))).join("\n");_.div(`${t}\n`)}return _.toString().replace(/\s*$/,"")},i.cacheHelpMessage=function(){w=this.help()},i.clearCachedHelpMessage=function(){w=void 0},i.hasCachedHelpMessage=function(){return!!w},i.showHelp=e=>{const s=t.getInternalMethods().getLoggerInstance();e||(e="error");("function"==typeof e?e:s[e])(i.help())},i.functionDescription=t=>["(",t.name?s.Parser.decamelize(t.name,"-"):n("generated-value"),")"].join(""),i.stringifiedValues=function(t,e){let s="";const n=e||", ",i=[].concat(t);return t&&i.length?(i.forEach((t=>{s.length&&(s+=n),s+=JSON.stringify(t)})),s):s};let _=null;i.version=t=>{_=t},i.showVersion=e=>{const s=t.getInternalMethods().getLoggerInstance();e||(e="error");("function"==typeof e?e:s[e])(_)},i.reset=function(t){return o=null,h=!1,l=[],c=!1,p=[],f=[],d=[],u=g(u,(e=>!t[e])),i};const M=[];return i.freeze=function(){M.push({failMessage:o,failureOutput:h,usages:l,usageDisabled:c,epilogs:p,examples:f,commands:d,descriptions:u})},i.unfreeze=function(){const t=M.pop();t&&({failMessage:o,failureOutput:h,usages:l,usageDisabled:c,epilogs:p,examples:f,commands:d,descriptions:u}=t)},i}function A(t){return"object"==typeof t}function P(t){return A(t)?t.indentation:0}function I(t){return A(t)?t.text:t}class N{constructor(t,e,s,n){var i,r,o;this.yargs=t,this.usage=e,this.command=s,this.shim=n,this.completionKey="get-yargs-completions",this.aliases=null,this.customCompletionFunction=null,this.zshShell=null!==(o=(null===(i=this.shim.getEnv("SHELL"))||void 0===i?void 0:i.includes("zsh"))||(null===(r=this.shim.getEnv("ZSH_NAME"))||void 0===r?void 0:r.includes("zsh")))&&void 0!==o&&o}defaultCompletion(t,e,s,n){const i=this.command.getCommandHandlers();for(let e=0,s=t.length;e<s;++e)if(i[t[e]]&&i[t[e]].builder){const s=i[t[e]].builder;if(E(s)){const t=this.yargs.getInternalMethods().reset();return s(t,!0),t.argv}}const r=[];this.commandCompletions(r,t,s),this.optionCompletions(r,t,e,s),n(null,r)}commandCompletions(t,e,s){const n=this.yargs.getInternalMethods().getContext().commands;s.match(/^-/)||n[n.length-1]===s||this.usage.getCommands().forEach((s=>{const n=o(s[0]).cmd;if(-1===e.indexOf(n))if(this.zshShell){const e=s[1]||"";t.push(n.replace(/:/g,"\\:")+":"+e)}else t.push(n)}))}optionCompletions(t,e,s,n){if(n.match(/^-/)||""===n&&0===t.length){const i=this.yargs.getOptions(),r=this.yargs.getGroups()[this.usage.getPositionalGroupName()]||[];Object.keys(i.key).forEach((o=>{const a=!!i.configuration["boolean-negation"]&&i.boolean.includes(o);r.includes(o)||this.argsContainKey(e,s,o,a)||(this.completeOptionKey(o,t,n),a&&i.default[o]&&this.completeOptionKey(`no-${o}`,t,n))}))}}argsContainKey(t,e,s,n){if(-1!==t.indexOf(`--${s}`))return!0;if(n&&-1!==t.indexOf(`--no-${s}`))return!0;if(this.aliases)for(const t of this.aliases[s])if(void 0!==e[t])return!0;return!1}completeOptionKey(t,e,s){const n=this.usage.getDescriptions(),i=!/^--/.test(s)&&(t=>/^[^0-9]$/.test(t))(t)?"-":"--";if(this.zshShell){const s=n[t]||"";e.push(i+`${t.replace(/:/g,"\\:")}:${s.replace("__yargsString__:","")}`)}else e.push(i+t)}customCompletion(t,e,s,n){if(d(this.customCompletionFunction,null,this.shim),this.customCompletionFunction.length<3){const t=this.customCompletionFunction(s,e);return f(t)?t.then((t=>{this.shim.process.nextTick((()=>{n(null,t)}))})).catch((t=>{this.shim.process.nextTick((()=>{n(t,void 0)}))})):n(null,t)}return function(t){return t.length>3}(this.customCompletionFunction)?this.customCompletionFunction(s,e,((i=n)=>this.defaultCompletion(t,e,s,i)),(t=>{n(null,t)})):this.customCompletionFunction(s,e,(t=>{n(null,t)}))}getCompletion(t,e){const s=t.length?t[t.length-1]:"",n=this.yargs.parse(t,!0),i=this.customCompletionFunction?n=>this.customCompletion(t,n,s,e):n=>this.defaultCompletion(t,n,s,e);return f(n)?n.then(i):i(n)}generateCompletionScript(t,e){let s=this.zshShell?'#compdef {{app_name}}\n###-begin-{{app_name}}-completions-###\n#\n# yargs command completion script\n#\n# Installation: {{app_path}} {{completion_command}} >> ~/.zshrc\n#    or {{app_path}} {{completion_command}} >> ~/.zsh_profile on OSX.\n#\n_{{app_name}}_yargs_completions()\n{\n  local reply\n  local si=$IFS\n  IFS=$\'\n\' reply=($(COMP_CWORD="$((CURRENT-1))" COMP_LINE="$BUFFER" COMP_POINT="$CURSOR" {{app_path}} --get-yargs-completions "${words[@]}"))\n  IFS=$si\n  _describe \'values\' reply\n}\ncompdef _{{app_name}}_yargs_completions {{app_name}}\n###-end-{{app_name}}-completions-###\n':'###-begin-{{app_name}}-completions-###\n#\n# yargs command completion script\n#\n# Installation: {{app_path}} {{completion_command}} >> ~/.bashrc\n#    or {{app_path}} {{completion_command}} >> ~/.bash_profile on OSX.\n#\n_{{app_name}}_yargs_completions()\n{\n    local cur_word args type_list\n\n    cur_word="${COMP_WORDS[COMP_CWORD]}"\n    args=("${COMP_WORDS[@]}")\n\n    # ask yargs to generate completions.\n    type_list=$({{app_path}} --get-yargs-completions "${args[@]}")\n\n    COMPREPLY=( $(compgen -W "${type_list}" -- ${cur_word}) )\n\n    # if no match was found, fall back to filename completion\n    if [ ${#COMPREPLY[@]} -eq 0 ]; then\n      COMPREPLY=()\n    fi\n\n    return 0\n}\ncomplete -o default -F _{{app_name}}_yargs_completions {{app_name}}\n###-end-{{app_name}}-completions-###\n';const n=this.shim.path.basename(t);return t.match(/\.js$/)&&(t=`./${t}`),s=s.replace(/{{app_name}}/g,n),s=s.replace(/{{completion_command}}/g,e),s.replace(/{{app_path}}/g,t)}registerFunction(t){this.customCompletionFunction=t}setParsed(t){this.aliases=t.aliases}}function D(t,e){if(0===t.length)return e.length;if(0===e.length)return t.length;const s=[];let n,i;for(n=0;n<=e.length;n++)s[n]=[n];for(i=0;i<=t.length;i++)s[0][i]=i;for(n=1;n<=e.length;n++)for(i=1;i<=t.length;i++)e.charAt(n-1)===t.charAt(i-1)?s[n][i]=s[n-1][i-1]:n>1&&i>1&&e.charAt(n-2)===t.charAt(i-1)&&e.charAt(n-1)===t.charAt(i-2)?s[n][i]=s[n-2][i-2]+1:s[n][i]=Math.min(s[n-1][i-1]+1,Math.min(s[n][i-1]+1,s[n-1][i]+1));return s[e.length][t.length]}const z=["$0","--","_"];var q,H,F,U,W,L,V,R,T,G,B,K,Y,J,Z,X,Q,tt,et,st,nt,it,rt,ot,at,ht,lt,ct,ft,dt,ut,pt;const gt=Symbol("copyDoubleDash"),mt=Symbol("copyDoubleDash"),yt=Symbol("deleteFromParserHintObject"),bt=Symbol("freeze"),vt=Symbol("getDollarZero"),Ot=Symbol("getParserConfiguration"),wt=Symbol("guessLocale"),Ct=Symbol("guessVersion"),jt=Symbol("parsePositionalNumbers"),_t=Symbol("pkgUp"),Mt=Symbol("populateParserHintArray"),kt=Symbol("populateParserHintSingleValueDictionary"),Et=Symbol("populateParserHintArrayDictionary"),xt=Symbol("populateParserHintDictionary"),St=Symbol("sanitizeKey"),$t=Symbol("setKey"),At=Symbol("unfreeze"),Pt=Symbol("validateAsync"),It=Symbol("getCommandInstance"),Nt=Symbol("getContext"),Dt=Symbol("getHasOutput"),zt=Symbol("getLoggerInstance"),qt=Symbol("getParseContext"),Ht=Symbol("getUsageInstance"),Ft=Symbol("getValidationInstance"),Ut=Symbol("hasParseCallback"),Wt=Symbol("postProcess"),Lt=Symbol("rebase"),Vt=Symbol("reset"),Rt=Symbol("runYargsParserAndExecuteCommands"),Tt=Symbol("runValidation"),Gt=Symbol("setHasOutput");class Bt{constructor(t=[],e,s,n){this.customScriptName=!1,this.parsed=!1,q.set(this,void 0),H.set(this,void 0),F.set(this,{commands:[],fullCommands:[]}),U.set(this,null),W.set(this,null),L.set(this,"show-hidden"),V.set(this,null),R.set(this,!0),T.set(this,!0),G.set(this,[]),B.set(this,void 0),K.set(this,{}),Y.set(this,!1),J.set(this,null),Z.set(this,void 0),X.set(this,""),Q.set(this,void 0),tt.set(this,void 0),et.set(this,{}),st.set(this,null),nt.set(this,null),it.set(this,{}),rt.set(this,{}),ot.set(this,void 0),at.set(this,!1),ht.set(this,void 0),lt.set(this,!1),ct.set(this,!1),ft.set(this,!1),dt.set(this,void 0),ut.set(this,null),pt.set(this,void 0),O(this,ht,n,"f"),O(this,ot,t,"f"),O(this,H,e,"f"),O(this,tt,s,"f"),O(this,B,new w(this),"f"),this.$0=this[vt](),this[Vt](),O(this,q,v(this,q,"f"),"f"),O(this,dt,v(this,dt,"f"),"f"),O(this,pt,v(this,pt,"f"),"f"),O(this,Q,v(this,Q,"f"),"f"),v(this,Q,"f").showHiddenOpt=v(this,L,"f"),O(this,Z,this[mt](),"f")}addHelpOpt(t,e){return h("[string|boolean] [string]",[t,e],arguments.length),v(this,J,"f")&&(this[yt](v(this,J,"f")),O(this,J,null,"f")),!1===t&&void 0===e||(O(this,J,"string"==typeof t?t:"help","f"),this.boolean(v(this,J,"f")),this.describe(v(this,J,"f"),e||v(this,dt,"f").deferY18nLookup("Show help"))),this}help(t,e){return this.addHelpOpt(t,e)}addShowHiddenOpt(t,e){if(h("[string|boolean] [string]",[t,e],arguments.length),!1===t&&void 0===e)return this;const s="string"==typeof t?t:v(this,L,"f");return this.boolean(s),this.describe(s,e||v(this,dt,"f").deferY18nLookup("Show hidden options")),v(this,Q,"f").showHiddenOpt=s,this}showHidden(t,e){return this.addShowHiddenOpt(t,e)}alias(t,e){return h("<object|string|array> [string|array]",[t,e],arguments.length),this[Et](this.alias.bind(this),"alias",t,e),this}array(t){return h("<array|string>",[t],arguments.length),this[Mt]("array",t),this}boolean(t){return h("<array|string>",[t],arguments.length),this[Mt]("boolean",t),this}check(t,e){return h("<function> [boolean]",[t,e],arguments.length),this.middleware(((e,s)=>j((()=>t(e)),(s=>(s?("string"==typeof s||s instanceof Error)&&v(this,dt,"f").fail(s.toString(),s):v(this,dt,"f").fail(v(this,ht,"f").y18n.__("Argument check failed: %s",t.toString())),e)),(t=>(v(this,dt,"f").fail(t.message?t.message:t.toString(),t),e)))),!1,e),this}choices(t,e){return h("<object|string|array> [string|array]",[t,e],arguments.length),this[Et](this.choices.bind(this),"choices",t,e),this}coerce(t,s){if(h("<object|string|array> [function]",[t,s],arguments.length),Array.isArray(t)){if(!s)throw new e("coerce callback must be provided");for(const e of t)this.coerce(e,s);return this}if("object"==typeof t){for(const e of Object.keys(t))this.coerce(e,t[e]);return this}if(!s)throw new e("coerce callback must be provided");return v(this,Q,"f").key[t]=!0,v(this,B,"f").addCoerceMiddleware(((n,i)=>{let r;return j((()=>(r=i.getAliases(),s(n[t]))),(e=>{if(n[t]=e,r[t])for(const s of r[t])n[s]=e;return n}),(t=>{throw new e(t.message)}))}),t),this}conflicts(t,e){return h("<string|object> [string|array]",[t,e],arguments.length),v(this,pt,"f").conflicts(t,e),this}config(t="config",e,s){return h("[object|string] [string|function] [function]",[t,e,s],arguments.length),"object"!=typeof t||Array.isArray(t)?("function"==typeof e&&(s=e,e=void 0),this.describe(t,e||v(this,dt,"f").deferY18nLookup("Path to JSON config file")),(Array.isArray(t)?t:[t]).forEach((t=>{v(this,Q,"f").config[t]=s||!0})),this):(t=i(t,v(this,H,"f"),this[Ot]()["deep-merge-config"]||!1,v(this,ht,"f")),v(this,Q,"f").configObjects=(v(this,Q,"f").configObjects||[]).concat(t),this)}completion(t,e,s){return h("[string] [string|boolean|function] [function]",[t,e,s],arguments.length),"function"==typeof e&&(s=e,e=void 0),O(this,W,t||v(this,W,"f")||"completion","f"),e||!1===e||(e="generate completion script"),this.command(v(this,W,"f"),e),s&&v(this,U,"f").registerFunction(s),this}command(t,e,s,n,i,r){return h("<string|array|object> [string|boolean] [function|object] [function] [array] [boolean|string]",[t,e,s,n,i,r],arguments.length),v(this,q,"f").addHandler(t,e,s,n,i,r),this}commands(t,e,s,n,i,r){return this.command(t,e,s,n,i,r)}commandDir(t,e){h("<string> [object]",[t,e],arguments.length);const s=v(this,tt,"f")||v(this,ht,"f").require;return v(this,q,"f").addDirectory(t,s,v(this,ht,"f").getCallerFile(),e),this}count(t){return h("<array|string>",[t],arguments.length),this[Mt]("count",t),this}default(t,e,s){return h("<object|string|array> [*] [string]",[t,e,s],arguments.length),s&&(u(t,v(this,ht,"f")),v(this,Q,"f").defaultDescription[t]=s),"function"==typeof e&&(u(t,v(this,ht,"f")),v(this,Q,"f").defaultDescription[t]||(v(this,Q,"f").defaultDescription[t]=v(this,dt,"f").functionDescription(e)),e=e.call()),this[kt](this.default.bind(this),"default",t,e),this}defaults(t,e,s){return this.default(t,e,s)}demandCommand(t=1,e,s,n){return h("[number] [number|string] [string|null|undefined] [string|null|undefined]",[t,e,s,n],arguments.length),"number"!=typeof e&&(s=e,e=1/0),this.global("_",!1),v(this,Q,"f").demandedCommands._={min:t,max:e,minMsg:s,maxMsg:n},this}demand(t,e,s){return Array.isArray(e)?(e.forEach((t=>{d(s,!0,v(this,ht,"f")),this.demandOption(t,s)})),e=1/0):"number"!=typeof e&&(s=e,e=1/0),"number"==typeof t?(d(s,!0,v(this,ht,"f")),this.demandCommand(t,e,s,s)):Array.isArray(t)?t.forEach((t=>{d(s,!0,v(this,ht,"f")),this.demandOption(t,s)})):"string"==typeof s?this.demandOption(t,s):!0!==s&&void 0!==s||this.demandOption(t),this}demandOption(t,e){return h("<object|string|array> [string]",[t,e],arguments.length),this[kt](this.demandOption.bind(this),"demandedOptions",t,e),this}deprecateOption(t,e){return h("<string> [string|boolean]",[t,e],arguments.length),v(this,Q,"f").deprecatedOptions[t]=e,this}describe(t,e){return h("<object|string|array> [string]",[t,e],arguments.length),this[$t](t,!0),v(this,dt,"f").describe(t,e),this}detectLocale(t){return h("<boolean>",[t],arguments.length),O(this,R,t,"f"),this}env(t){return h("[string|boolean]",[t],arguments.length),!1===t?delete v(this,Q,"f").envPrefix:v(this,Q,"f").envPrefix=t||"",this}epilogue(t){return h("<string>",[t],arguments.length),v(this,dt,"f").epilog(t),this}epilog(t){return this.epilogue(t)}example(t,e){return h("<string|array> [string]",[t,e],arguments.length),Array.isArray(t)?t.forEach((t=>this.example(...t))):v(this,dt,"f").example(t,e),this}exit(t,e){O(this,Y,!0,"f"),O(this,V,e,"f"),v(this,T,"f")&&v(this,ht,"f").process.exit(t)}exitProcess(t=!0){return h("[boolean]",[t],arguments.length),O(this,T,t,"f"),this}fail(t){if(h("<function|boolean>",[t],arguments.length),"boolean"==typeof t&&!1!==t)throw new e("Invalid first argument. Expected function or boolean 'false'");return v(this,dt,"f").failFn(t),this}getAliases(){return this.parsed?this.parsed.aliases:{}}async getCompletion(t,e){return h("<array> [function]",[t,e],arguments.length),e?v(this,U,"f").getCompletion(t,e):new Promise(((e,s)=>{v(this,U,"f").getCompletion(t,((t,n)=>{t?s(t):e(n)}))}))}getDemandedOptions(){return h([],0),v(this,Q,"f").demandedOptions}getDemandedCommands(){return h([],0),v(this,Q,"f").demandedCommands}getDeprecatedOptions(){return h([],0),v(this,Q,"f").deprecatedOptions}getDetectLocale(){return v(this,R,"f")}getExitProcess(){return v(this,T,"f")}getGroups(){return Object.assign({},v(this,K,"f"),v(this,rt,"f"))}getHelp(){if(O(this,Y,!0,"f"),!v(this,dt,"f").hasCachedHelpMessage()){if(!this.parsed){const t=this[Rt](v(this,ot,"f"),void 0,void 0,0,!0);if(f(t))return t.then((()=>v(this,dt,"f").help()))}const t=v(this,q,"f").runDefaultBuilderOn(this);if(f(t))return t.then((()=>v(this,dt,"f").help()))}return Promise.resolve(v(this,dt,"f").help())}getOptions(){return v(this,Q,"f")}getStrict(){return v(this,lt,"f")}getStrictCommands(){return v(this,ct,"f")}getStrictOptions(){return v(this,ft,"f")}global(t,e){return h("<string|array> [boolean]",[t,e],arguments.length),t=[].concat(t),!1!==e?v(this,Q,"f").local=v(this,Q,"f").local.filter((e=>-1===t.indexOf(e))):t.forEach((t=>{v(this,Q,"f").local.includes(t)||v(this,Q,"f").local.push(t)})),this}group(t,e){h("<string|array> <string>",[t,e],arguments.length);const s=v(this,rt,"f")[e]||v(this,K,"f")[e];v(this,rt,"f")[e]&&delete v(this,rt,"f")[e];const n={};return v(this,K,"f")[e]=(s||[]).concat(t).filter((t=>!n[t]&&(n[t]=!0))),this}hide(t){return h("<string>",[t],arguments.length),v(this,Q,"f").hiddenOptions.push(t),this}implies(t,e){return h("<string|object> [number|string|array]",[t,e],arguments.length),v(this,pt,"f").implies(t,e),this}locale(t){return h("[string]",[t],arguments.length),t?(O(this,R,!1,"f"),v(this,ht,"f").y18n.setLocale(t),this):(this[wt](),v(this,ht,"f").y18n.getLocale())}middleware(t,e,s){return v(this,B,"f").addMiddleware(t,!!e,s)}nargs(t,e){return h("<string|object|array> [number]",[t,e],arguments.length),this[kt](this.nargs.bind(this),"narg",t,e),this}normalize(t){return h("<array|string>",[t],arguments.length),this[Mt]("normalize",t),this}number(t){return h("<array|string>",[t],arguments.length),this[Mt]("number",t),this}option(t,e){if(h("<string|object> [object]",[t,e],arguments.length),"object"==typeof t)Object.keys(t).forEach((e=>{this.options(e,t[e])}));else{"object"!=typeof e&&(e={}),v(this,Q,"f").key[t]=!0,e.alias&&this.alias(t,e.alias);const s=e.deprecate||e.deprecated;s&&this.deprecateOption(t,s);const n=e.demand||e.required||e.require;n&&this.demand(t,n),e.demandOption&&this.demandOption(t,"string"==typeof e.demandOption?e.demandOption:void 0),e.conflicts&&this.conflicts(t,e.conflicts),"default"in e&&this.default(t,e.default),void 0!==e.implies&&this.implies(t,e.implies),void 0!==e.nargs&&this.nargs(t,e.nargs),e.config&&this.config(t,e.configParser),e.normalize&&this.normalize(t),e.choices&&this.choices(t,e.choices),e.coerce&&this.coerce(t,e.coerce),e.group&&this.group(t,e.group),(e.boolean||"boolean"===e.type)&&(this.boolean(t),e.alias&&this.boolean(e.alias)),(e.array||"array"===e.type)&&(this.array(t),e.alias&&this.array(e.alias)),(e.number||"number"===e.type)&&(this.number(t),e.alias&&this.number(e.alias)),(e.string||"string"===e.type)&&(this.string(t),e.alias&&this.string(e.alias)),(e.count||"count"===e.type)&&this.count(t),"boolean"==typeof e.global&&this.global(t,e.global),e.defaultDescription&&(v(this,Q,"f").defaultDescription[t]=e.defaultDescription),e.skipValidation&&this.skipValidation(t);const i=e.describe||e.description||e.desc;this.describe(t,i),e.hidden&&this.hide(t),e.requiresArg&&this.requiresArg(t)}return this}options(t,e){return this.option(t,e)}parse(t,e,s){h("[string|array] [function|boolean|object] [function]",[t,e,s],arguments.length),this[bt](),void 0===t&&(t=v(this,ot,"f")),"object"==typeof e&&(O(this,nt,e,"f"),e=s),"function"==typeof e&&(O(this,st,e,"f"),e=!1),e||O(this,ot,t,"f"),v(this,st,"f")&&O(this,T,!1,"f");const n=this[Rt](t,!!e),i=this.parsed;return v(this,U,"f").setParsed(this.parsed),f(n)?n.then((t=>(v(this,st,"f")&&v(this,st,"f").call(this,v(this,V,"f"),t,v(this,X,"f")),t))).catch((t=>{throw v(this,st,"f")&&v(this,st,"f")(t,this.parsed.argv,v(this,X,"f")),t})).finally((()=>{this[At](),this.parsed=i})):(v(this,st,"f")&&v(this,st,"f").call(this,v(this,V,"f"),n,v(this,X,"f")),this[At](),this.parsed=i,n)}parseAsync(t,e,s){const n=this.parse(t,e,s);return f(n)?n:Promise.resolve(n)}parseSync(t,s,n){const i=this.parse(t,s,n);if(f(i))throw new e(".parseSync() must not be used with asynchronous builders, handlers, or middleware");return i}parserConfiguration(t){return h("<object>",[t],arguments.length),O(this,et,t,"f"),this}pkgConf(t,e){h("<string> [string]",[t,e],arguments.length);let s=null;const n=this[_t](e||v(this,H,"f"));return n[t]&&"object"==typeof n[t]&&(s=i(n[t],e||v(this,H,"f"),this[Ot]()["deep-merge-config"]||!1,v(this,ht,"f")),v(this,Q,"f").configObjects=(v(this,Q,"f").configObjects||[]).concat(s)),this}positional(t,e){h("<string> <object>",[t,e],arguments.length);const s=["default","defaultDescription","implies","normalize","choices","conflicts","coerce","type","describe","desc","description","alias"];e=g(e,((t,e)=>!("type"===t&&!["string","number","boolean"].includes(e))&&s.includes(t)));const n=v(this,F,"f").fullCommands[v(this,F,"f").fullCommands.length-1],i=n?v(this,q,"f").cmdToParseOptions(n):{array:[],alias:{},default:{},demand:{}};return p(i).forEach((s=>{const n=i[s];Array.isArray(n)?-1!==n.indexOf(t)&&(e[s]=!0):n[t]&&!(s in e)&&(e[s]=n[t])})),this.group(t,v(this,dt,"f").getPositionalGroupName()),this.option(t,e)}recommendCommands(t=!0){return h("[boolean]",[t],arguments.length),O(this,at,t,"f"),this}required(t,e,s){return this.demand(t,e,s)}require(t,e,s){return this.demand(t,e,s)}requiresArg(t){return h("<array|string|object> [number]",[t],arguments.length),"string"==typeof t&&v(this,Q,"f").narg[t]||this[kt](this.requiresArg.bind(this),"narg",t,NaN),this}showCompletionScript(t,e){return h("[string] [string]",[t,e],arguments.length),t=t||this.$0,v(this,Z,"f").log(v(this,U,"f").generateCompletionScript(t,e||v(this,W,"f")||"completion")),this}showHelp(t){if(h("[string|function]",[t],arguments.length),O(this,Y,!0,"f"),!v(this,dt,"f").hasCachedHelpMessage()){if(!this.parsed){const e=this[Rt](v(this,ot,"f"),void 0,void 0,0,!0);if(f(e))return e.then((()=>{v(this,dt,"f").showHelp(t)})),this}const e=v(this,q,"f").runDefaultBuilderOn(this);if(f(e))return e.then((()=>{v(this,dt,"f").showHelp(t)})),this}return v(this,dt,"f").showHelp(t),this}scriptName(t){return this.customScriptName=!0,this.$0=t,this}showHelpOnFail(t,e){return h("[boolean|string] [string]",[t,e],arguments.length),v(this,dt,"f").showHelpOnFail(t,e),this}showVersion(t){return h("[string|function]",[t],arguments.length),v(this,dt,"f").showVersion(t),this}skipValidation(t){return h("<array|string>",[t],arguments.length),this[Mt]("skipValidation",t),this}strict(t){return h("[boolean]",[t],arguments.length),O(this,lt,!1!==t,"f"),this}strictCommands(t){return h("[boolean]",[t],arguments.length),O(this,ct,!1!==t,"f"),this}strictOptions(t){return h("[boolean]",[t],arguments.length),O(this,ft,!1!==t,"f"),this}string(t){return h("<array|string>",[t],arguments.length),this[Mt]("string",t),this}terminalWidth(){return h([],0),v(this,ht,"f").process.stdColumns}updateLocale(t){return this.updateStrings(t)}updateStrings(t){return h("<object>",[t],arguments.length),O(this,R,!1,"f"),v(this,ht,"f").y18n.updateLocale(t),this}usage(t,s,n,i){if(h("<string|null|undefined> [string|boolean] [function|object] [function]",[t,s,n,i],arguments.length),void 0!==s){if(d(t,null,v(this,ht,"f")),(t||"").match(/^\$0( |$)/))return this.command(t,s,n,i);throw new e(".usage() description must start with $0 if being used as alias for .command()")}return v(this,dt,"f").usage(t),this}version(t,e,s){const n="version";if(h("[boolean|string] [string] [string]",[t,e,s],arguments.length),v(this,ut,"f")&&(this[yt](v(this,ut,"f")),v(this,dt,"f").version(void 0),O(this,ut,null,"f")),0===arguments.length)s=this[Ct](),t=n;else if(1===arguments.length){if(!1===t)return this;s=t,t=n}else 2===arguments.length&&(s=e,e=void 0);return O(this,ut,"string"==typeof t?t:n,"f"),e=e||v(this,dt,"f").deferY18nLookup("Show version number"),v(this,dt,"f").version(s||void 0),this.boolean(v(this,ut,"f")),this.describe(v(this,ut,"f"),e),this}wrap(t){return h("<number|null|undefined>",[t],arguments.length),v(this,dt,"f").wrap(t),this}[(q=new WeakMap,H=new WeakMap,F=new WeakMap,U=new WeakMap,W=new WeakMap,L=new WeakMap,V=new WeakMap,R=new WeakMap,T=new WeakMap,G=new WeakMap,B=new WeakMap,K=new WeakMap,Y=new WeakMap,J=new WeakMap,Z=new WeakMap,X=new WeakMap,Q=new WeakMap,tt=new WeakMap,et=new WeakMap,st=new WeakMap,nt=new WeakMap,it=new WeakMap,rt=new WeakMap,ot=new WeakMap,at=new WeakMap,ht=new WeakMap,lt=new WeakMap,ct=new WeakMap,ft=new WeakMap,dt=new WeakMap,ut=new WeakMap,pt=new WeakMap,gt)](t){if(!t._||!t["--"])return t;t._.push.apply(t._,t["--"]);try{delete t["--"]}catch(t){}return t}[mt](){return{log:(...t)=>{this[Ut]()||console.log(...t),O(this,Y,!0,"f"),v(this,X,"f").length&&O(this,X,v(this,X,"f")+"\n","f"),O(this,X,v(this,X,"f")+t.join(" "),"f")},error:(...t)=>{this[Ut]()||console.error(...t),O(this,Y,!0,"f"),v(this,X,"f").length&&O(this,X,v(this,X,"f")+"\n","f"),O(this,X,v(this,X,"f")+t.join(" "),"f")}}}[yt](t){p(v(this,Q,"f")).forEach((e=>{if("configObjects"===e)return;const s=v(this,Q,"f")[e];Array.isArray(s)?s.includes(t)&&s.splice(s.indexOf(t),1):"object"==typeof s&&delete s[t]})),delete v(this,dt,"f").getDescriptions()[t]}[bt](){v(this,G,"f").push({options:v(this,Q,"f"),configObjects:v(this,Q,"f").configObjects.slice(0),exitProcess:v(this,T,"f"),groups:v(this,K,"f"),strict:v(this,lt,"f"),strictCommands:v(this,ct,"f"),strictOptions:v(this,ft,"f"),completionCommand:v(this,W,"f"),output:v(this,X,"f"),exitError:v(this,V,"f"),hasOutput:v(this,Y,"f"),parsed:this.parsed,parseFn:v(this,st,"f"),parseContext:v(this,nt,"f")}),v(this,dt,"f").freeze(),v(this,pt,"f").freeze(),v(this,q,"f").freeze(),v(this,B,"f").freeze()}[vt](){let t,e="";return t=/\b(node|iojs|electron)(\.exe)?$/.test(v(this,ht,"f").process.argv()[0])?v(this,ht,"f").process.argv().slice(1,2):v(this,ht,"f").process.argv().slice(0,1),e=t.map((t=>{const e=this[Lt](v(this,H,"f"),t);return t.match(/^(\/|([a-zA-Z]:)?\\)/)&&e.length<t.length?e:t})).join(" ").trim(),v(this,ht,"f").getEnv("_")&&v(this,ht,"f").getProcessArgvBin()===v(this,ht,"f").getEnv("_")&&(e=v(this,ht,"f").getEnv("_").replace(`${v(this,ht,"f").path.dirname(v(this,ht,"f").process.execPath())}/`,"")),e}[Ot](){return v(this,et,"f")}[wt](){if(!v(this,R,"f"))return;const t=v(this,ht,"f").getEnv("LC_ALL")||v(this,ht,"f").getEnv("LC_MESSAGES")||v(this,ht,"f").getEnv("LANG")||v(this,ht,"f").getEnv("LANGUAGE")||"en_US";this.locale(t.replace(/[.:].*/,""))}[Ct](){return this[_t]().version||"unknown"}[jt](t){const e=t["--"]?t["--"]:t._;for(let t,s=0;void 0!==(t=e[s]);s++)v(this,ht,"f").Parser.looksLikeNumber(t)&&Number.isSafeInteger(Math.floor(parseFloat(`${t}`)))&&(e[s]=Number(t));return t}[_t](t){const e=t||"*";if(v(this,it,"f")[e])return v(this,it,"f")[e];let s={};try{let e=t||v(this,ht,"f").mainFilename;!t&&v(this,ht,"f").path.extname(e)&&(e=v(this,ht,"f").path.dirname(e));const n=v(this,ht,"f").findUp(e,((t,e)=>e.includes("package.json")?"package.json":void 0));d(n,void 0,v(this,ht,"f")),s=JSON.parse(v(this,ht,"f").readFileSync(n,"utf8"))}catch(t){}return v(this,it,"f")[e]=s||{},v(this,it,"f")[e]}[Mt](t,e){(e=[].concat(e)).forEach((e=>{e=this[St](e),v(this,Q,"f")[t].push(e)}))}[kt](t,e,s,n){this[xt](t,e,s,n,((t,e,s)=>{v(this,Q,"f")[t][e]=s}))}[Et](t,e,s,n){this[xt](t,e,s,n,((t,e,s)=>{v(this,Q,"f")[t][e]=(v(this,Q,"f")[t][e]||[]).concat(s)}))}[xt](t,e,s,n,i){if(Array.isArray(s))s.forEach((e=>{t(e,n)}));else if((t=>"object"==typeof t)(s))for(const e of p(s))t(e,s[e]);else i(e,this[St](s),n)}[St](t){return"__proto__"===t?"___proto___":t}[$t](t,e){return this[kt](this[$t].bind(this),"key",t,e),this}[At](){var t,e,s,n,i,r,o,a,h,l,c,f;const u=v(this,G,"f").pop();let p;d(u,void 0,v(this,ht,"f")),t=this,e=this,s=this,n=this,i=this,r=this,o=this,a=this,h=this,l=this,c=this,f=this,({options:{set value(e){O(t,Q,e,"f")}}.value,configObjects:p,exitProcess:{set value(t){O(e,T,t,"f")}}.value,groups:{set value(t){O(s,K,t,"f")}}.value,output:{set value(t){O(n,X,t,"f")}}.value,exitError:{set value(t){O(i,V,t,"f")}}.value,hasOutput:{set value(t){O(r,Y,t,"f")}}.value,parsed:this.parsed,strict:{set value(t){O(o,lt,t,"f")}}.value,strictCommands:{set value(t){O(a,ct,t,"f")}}.value,strictOptions:{set value(t){O(h,ft,t,"f")}}.value,completionCommand:{set value(t){O(l,W,t,"f")}}.value,parseFn:{set value(t){O(c,st,t,"f")}}.value,parseContext:{set value(t){O(f,nt,t,"f")}}.value}=u),v(this,Q,"f").configObjects=p,v(this,dt,"f").unfreeze(),v(this,pt,"f").unfreeze(),v(this,q,"f").unfreeze(),v(this,B,"f").unfreeze()}[Pt](t,e){return j(e,(e=>(t(e),e)))}getInternalMethods(){return{getCommandInstance:this[It].bind(this),getContext:this[Nt].bind(this),getHasOutput:this[Dt].bind(this),getLoggerInstance:this[zt].bind(this),getParseContext:this[qt].bind(this),getParserConfiguration:this[Ot].bind(this),getUsageInstance:this[Ht].bind(this),getValidationInstance:this[Ft].bind(this),hasParseCallback:this[Ut].bind(this),postProcess:this[Wt].bind(this),reset:this[Vt].bind(this),runValidation:this[Tt].bind(this),runYargsParserAndExecuteCommands:this[Rt].bind(this),setHasOutput:this[Gt].bind(this)}}[It](){return v(this,q,"f")}[Nt](){return v(this,F,"f")}[Dt](){return v(this,Y,"f")}[zt](){return v(this,Z,"f")}[qt](){return v(this,nt,"f")||{}}[Ht](){return v(this,dt,"f")}[Ft](){return v(this,pt,"f")}[Ut](){return!!v(this,st,"f")}[Wt](t,e,s,n){if(s)return t;if(f(t))return t;e||(t=this[gt](t));return(this[Ot]()["parse-positional-numbers"]||void 0===this[Ot]()["parse-positional-numbers"])&&(t=this[jt](t)),n&&(t=C(t,this,v(this,B,"f").getMiddleware(),!1)),t}[Vt](t={}){O(this,Q,v(this,Q,"f")||{},"f");const e={};e.local=v(this,Q,"f").local||[],e.configObjects=v(this,Q,"f").configObjects||[];const s={};e.local.forEach((e=>{s[e]=!0,(t[e]||[]).forEach((t=>{s[t]=!0}))})),Object.assign(v(this,rt,"f"),Object.keys(v(this,K,"f")).reduce(((t,e)=>{const n=v(this,K,"f")[e].filter((t=>!(t in s)));return n.length>0&&(t[e]=n),t}),{})),O(this,K,{},"f");return["array","boolean","string","skipValidation","count","normalize","number","hiddenOptions"].forEach((t=>{e[t]=(v(this,Q,"f")[t]||[]).filter((t=>!s[t]))})),["narg","key","alias","default","defaultDescription","config","choices","demandedOptions","demandedCommands","deprecatedOptions"].forEach((t=>{e[t]=g(v(this,Q,"f")[t],(t=>!s[t]))})),e.envPrefix=v(this,Q,"f").envPrefix,O(this,Q,e,"f"),O(this,dt,v(this,dt,"f")?v(this,dt,"f").reset(s):$(this,v(this,ht,"f")),"f"),O(this,pt,v(this,pt,"f")?v(this,pt,"f").reset(s):function(t,e,s){const n=s.y18n.__,i=s.y18n.__n,r={nonOptionCount:function(s){const n=t.getDemandedCommands(),r=s._.length+(s["--"]?s["--"].length:0)-t.getInternalMethods().getContext().commands.length;n._&&(r<n._.min||r>n._.max)&&(r<n._.min?void 0!==n._.minMsg?e.fail(n._.minMsg?n._.minMsg.replace(/\$0/g,r.toString()).replace(/\$1/,n._.min.toString()):null):e.fail(i("Not enough non-option arguments: got %s, need at least %s","Not enough non-option arguments: got %s, need at least %s",r,r.toString(),n._.min.toString())):r>n._.max&&(void 0!==n._.maxMsg?e.fail(n._.maxMsg?n._.maxMsg.replace(/\$0/g,r.toString()).replace(/\$1/,n._.max.toString()):null):e.fail(i("Too many non-option arguments: got %s, maximum of %s","Too many non-option arguments: got %s, maximum of %s",r,r.toString(),n._.max.toString()))))},positionalCount:function(t,s){s<t&&e.fail(i("Not enough non-option arguments: got %s, need at least %s","Not enough non-option arguments: got %s, need at least %s",s,s+"",t+""))},requiredArguments:function(t,s){let n=null;for(const e of Object.keys(s))Object.prototype.hasOwnProperty.call(t,e)&&void 0!==t[e]||(n=n||{},n[e]=s[e]);if(n){const t=[];for(const e of Object.keys(n)){const s=n[e];s&&t.indexOf(s)<0&&t.push(s)}const s=t.length?`\n${t.join("\n")}`:"";e.fail(i("Missing required argument: %s","Missing required arguments: %s",Object.keys(n).length,Object.keys(n).join(", ")+s))}},unknownArguments:function(s,n,o,a,h=!0){var l;const c=t.getInternalMethods().getCommandInstance().getCommands(),f=[],d=t.getInternalMethods().getContext();if(Object.keys(s).forEach((e=>{z.includes(e)||Object.prototype.hasOwnProperty.call(o,e)||Object.prototype.hasOwnProperty.call(t.getInternalMethods().getParseContext(),e)||r.isValidAndSomeAliasIsNotNew(e,n)||f.push(e)})),h&&(d.commands.length>0||c.length>0||a)&&s._.slice(d.commands.length).forEach((t=>{c.includes(""+t)||f.push(""+t)})),h){const e=(null===(l=t.getDemandedCommands()._)||void 0===l?void 0:l.max)||0,n=d.commands.length+e;n<s._.length&&s._.slice(n).forEach((t=>{t=String(t),d.commands.includes(t)||f.includes(t)||f.push(t)}))}f.length&&e.fail(i("Unknown argument: %s","Unknown arguments: %s",f.length,f.join(", ")))},unknownCommands:function(s){const n=t.getInternalMethods().getCommandInstance().getCommands(),r=[],o=t.getInternalMethods().getContext();return(o.commands.length>0||n.length>0)&&s._.slice(o.commands.length).forEach((t=>{n.includes(""+t)||r.push(""+t)})),r.length>0&&(e.fail(i("Unknown command: %s","Unknown commands: %s",r.length,r.join(", "))),!0)},isValidAndSomeAliasIsNotNew:function(e,s){if(!Object.prototype.hasOwnProperty.call(s,e))return!1;const n=t.parsed.newAliases;return[e,...s[e]].some((t=>!Object.prototype.hasOwnProperty.call(n,t)||!n[e]))},limitedChoices:function(s){const i=t.getOptions(),r={};if(!Object.keys(i.choices).length)return;Object.keys(s).forEach((t=>{-1===z.indexOf(t)&&Object.prototype.hasOwnProperty.call(i.choices,t)&&[].concat(s[t]).forEach((e=>{-1===i.choices[t].indexOf(e)&&void 0!==e&&(r[t]=(r[t]||[]).concat(e))}))}));const o=Object.keys(r);if(!o.length)return;let a=n("Invalid values:");o.forEach((t=>{a+=`\n  ${n("Argument: %s, Given: %s, Choices: %s",t,e.stringifiedValues(r[t]),e.stringifiedValues(i.choices[t]))}`})),e.fail(a)}};let o={};function a(t,e){const s=Number(e);return"number"==typeof(e=isNaN(s)?e:s)?e=t._.length>=e:e.match(/^--no-.+/)?(e=e.match(/^--no-(.+)/)[1],e=!Object.prototype.hasOwnProperty.call(t,e)):e=Object.prototype.hasOwnProperty.call(t,e),e}r.implies=function(e,n){h("<string|object> [array|number|string]",[e,n],arguments.length),"object"==typeof e?Object.keys(e).forEach((t=>{r.implies(t,e[t])})):(t.global(e),o[e]||(o[e]=[]),Array.isArray(n)?n.forEach((t=>r.implies(e,t))):(d(n,void 0,s),o[e].push(n)))},r.getImplied=function(){return o},r.implications=function(t){const s=[];if(Object.keys(o).forEach((e=>{const n=e;(o[e]||[]).forEach((e=>{let i=n;const r=e;i=a(t,i),e=a(t,e),i&&!e&&s.push(` ${n} -> ${r}`)}))})),s.length){let t=`${n("Implications failed:")}\n`;s.forEach((e=>{t+=e})),e.fail(t)}};let l={};r.conflicts=function(e,s){h("<string|object> [array|string]",[e,s],arguments.length),"object"==typeof e?Object.keys(e).forEach((t=>{r.conflicts(t,e[t])})):(t.global(e),l[e]||(l[e]=[]),Array.isArray(s)?s.forEach((t=>r.conflicts(e,t))):l[e].push(s))},r.getConflicting=()=>l,r.conflicting=function(t){Object.keys(t).forEach((s=>{l[s]&&l[s].forEach((i=>{i&&void 0!==t[s]&&void 0!==t[i]&&e.fail(n("Arguments %s and %s are mutually exclusive",s,i))}))}))},r.recommendCommands=function(t,s){s=s.sort(((t,e)=>e.length-t.length));let i=null,r=1/0;for(let e,n=0;void 0!==(e=s[n]);n++){const s=D(t,e);s<=3&&s<r&&(r=s,i=e)}i&&e.fail(n("Did you mean %s?",i))},r.reset=function(t){return o=g(o,(e=>!t[e])),l=g(l,(e=>!t[e])),r};const c=[];return r.freeze=function(){c.push({implied:o,conflicting:l})},r.unfreeze=function(){const t=c.pop();d(t,void 0,s),({implied:o,conflicting:l}=t)},r}(this,v(this,dt,"f"),v(this,ht,"f")),"f"),O(this,q,v(this,q,"f")?v(this,q,"f").reset():function(t,e,s,n){return new M(t,e,s,n)}(v(this,dt,"f"),v(this,pt,"f"),v(this,B,"f"),v(this,ht,"f")),"f"),v(this,U,"f")||O(this,U,function(t,e,s,n){return new N(t,e,s,n)}(this,v(this,dt,"f"),v(this,q,"f"),v(this,ht,"f")),"f"),v(this,B,"f").reset(),O(this,W,null,"f"),O(this,X,"","f"),O(this,V,null,"f"),O(this,Y,!1,"f"),this.parsed=!1,this}[Lt](t,e){return v(this,ht,"f").path.relative(t,e)}[Rt](t,s,n,i=0,r=!1){let o=!!n||r;t=t||v(this,ot,"f"),v(this,Q,"f").__=v(this,ht,"f").y18n.__,v(this,Q,"f").configuration=this[Ot]();const a=!!v(this,Q,"f").configuration["populate--"],h=Object.assign({},v(this,Q,"f").configuration,{"populate--":!0}),l=v(this,ht,"f").Parser.detailed(t,Object.assign({},v(this,Q,"f"),{configuration:{"parse-positional-numbers":!1,...h}})),c=Object.assign(l.argv,v(this,nt,"f"));let d;const u=l.aliases;let p=!1,g=!1;Object.keys(c).forEach((t=>{t===v(this,J,"f")&&c[t]?p=!0:t===v(this,ut,"f")&&c[t]&&(g=!0)})),c.$0=this.$0,this.parsed=l,0===i&&v(this,dt,"f").clearCachedHelpMessage();try{if(this[wt](),s)return this[Wt](c,a,!!n,!1);if(v(this,J,"f")){[v(this,J,"f")].concat(u[v(this,J,"f")]||[]).filter((t=>t.length>1)).includes(""+c._[c._.length-1])&&(c._.pop(),p=!0)}const h=v(this,q,"f").getCommands(),m=v(this,U,"f").completionKey in c,y=p||m||r;if(c._.length){if(h.length){let t;for(let e,s=i||0;void 0!==c._[s];s++){if(e=String(c._[s]),h.includes(e)&&e!==v(this,W,"f")){const t=v(this,q,"f").runCommand(e,this,l,s+1,r,p||g||r);return this[Wt](t,a,!!n,!1)}if(!t&&e!==v(this,W,"f")){t=e;break}}!v(this,q,"f").hasDefaultCommand()&&v(this,at,"f")&&t&&!y&&v(this,pt,"f").recommendCommands(t,h)}v(this,W,"f")&&c._.includes(v(this,W,"f"))&&!m&&(v(this,T,"f")&&x(!0),this.showCompletionScript(),this.exit(0))}if(v(this,q,"f").hasDefaultCommand()&&!y){const t=v(this,q,"f").runCommand(null,this,l,0,r,p||g||r);return this[Wt](t,a,!!n,!1)}if(m){v(this,T,"f")&&x(!0);const s=(t=[].concat(t)).slice(t.indexOf(`--${v(this,U,"f").completionKey}`)+1);return v(this,U,"f").getCompletion(s,((t,s)=>{if(t)throw new e(t.message);(s||[]).forEach((t=>{v(this,Z,"f").log(t)})),this.exit(0)})),this[Wt](c,!a,!!n,!1)}if(v(this,Y,"f")||(p?(v(this,T,"f")&&x(!0),o=!0,this.showHelp("log"),this.exit(0)):g&&(v(this,T,"f")&&x(!0),o=!0,v(this,dt,"f").showVersion("log"),this.exit(0))),!o&&v(this,Q,"f").skipValidation.length>0&&(o=Object.keys(c).some((t=>v(this,Q,"f").skipValidation.indexOf(t)>=0&&!0===c[t]))),!o){if(l.error)throw new e(l.error.message);if(!m){const t=this[Tt](u,{},l.error);n||(d=C(c,this,v(this,B,"f").getMiddleware(),!0)),d=this[Pt](t,null!=d?d:c),f(d)&&!n&&(d=d.then((()=>C(c,this,v(this,B,"f").getMiddleware(),!1))))}}}catch(t){if(!(t instanceof e))throw t;v(this,dt,"f").fail(t.message,t)}return this[Wt](null!=d?d:c,a,!!n,!0)}[Tt](t,s,n,i){const r={...this.getDemandedOptions()};return o=>{if(n)throw new e(n.message);v(this,pt,"f").nonOptionCount(o),v(this,pt,"f").requiredArguments(o,r);let a=!1;v(this,ct,"f")&&(a=v(this,pt,"f").unknownCommands(o)),v(this,lt,"f")&&!a?v(this,pt,"f").unknownArguments(o,t,s,!!i):v(this,ft,"f")&&v(this,pt,"f").unknownArguments(o,t,{},!1,!1),v(this,pt,"f").limitedChoices(o),v(this,pt,"f").implications(o),v(this,pt,"f").conflicting(o)}}[Gt](){O(this,Y,!0,"f")}}var Kt,Yt;const{readFileSync:Jt}=require("fs"),{inspect:Zt}=require("util"),{resolve:Xt}=require("path"),Qt=require("y18n"),te=require("yargs-parser");var ee,se={assert:{notStrictEqual:t.notStrictEqual,strictEqual:t.strictEqual},cliui:require("cliui"),findUp:require("escalade/sync"),getEnv:t=>process.env[t],getCallerFile:require("get-caller-file"),getProcessArgvBin:y,inspect:Zt,mainFilename:null!==(Yt=null===(Kt=null===require||void 0===require?void 0:require.main)||void 0===Kt?void 0:Kt.filename)&&void 0!==Yt?Yt:process.cwd(),Parser:te,path:require("path"),process:{argv:()=>process.argv,cwd:process.cwd,execPath:()=>process.execPath,exit:t=>{process.exit(t)},nextTick:process.nextTick,stdColumns:void 0!==process.stdout.columns?process.stdout.columns:null},readFileSync:Jt,require:require,requireDirectory:require("require-directory"),stringWidth:require("string-width"),y18n:Qt({directory:Xt(__dirname,"../locales"),updateFiles:!1})};const ne=(null===(ee=null===process||void 0===process?void 0:process.env)||void 0===ee?void 0:ee.YARGS_MIN_NODE_VERSION)?Number(process.env.YARGS_MIN_NODE_VERSION):12;if(process&&process.version){if(Number(process.version.match(/v([^.]+)/)[1])<ne)throw Error(`yargs supports a minimum Node.js version of ${ne}. Read our version support policy: https://github.com/yargs/yargs#supported-nodejs-versions`)}const ie=require("yargs-parser");var re,oe={applyExtends:i,cjsPlatformShim:se,Yargs:(re=se,(t=[],e=re.process.cwd(),s)=>{const n=new Bt(t,e,s,re);return Object.defineProperty(n,"argv",{get:()=>n.parse(),enumerable:!0}),n.help(),n.version(),n}),argsert:h,isPromise:f,objFilter:g,parseCommand:o,Parser:ie,processArgv:b,YError:e};module.exports=oe;
